import { supabase } from "@/lib/supabase";
import { HeroSection } from "@/types/hero";
import { BusinessSection } from "@/types/business";
import { WhySection } from "@/services/why-section.service";
import { EssentialSupportSection } from "@/types/essential-support";
import { SetupProcessDisplayData } from "@/types/setup-process";
import { NewCompanySection } from "@/types/new-company";
import { DynamicCellDisplayData } from "@/types/dynamic-cell";
import { getSetupProcessData } from "@/services/setup-process.service";

/**
 * Wrapper function to add timeout to any promise
 */
function withTimeout<T>(promise: Promise<T>, timeoutMs: number = 10000): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
}

export interface HomePageData {
    hero: HeroSection | null;
    business: BusinessSection | null;
    whySection: WhySection | null;
    essentialSupport: EssentialSupportSection | null;
    setupProcess: SetupProcessDisplayData | null;
    newCompany: {
        section: NewCompanySection | null;
        images: any | null;
    };
    dynamicCell: DynamicCellDisplayData | null;
}

/**
 * Fetches all home page data in a single server-side call
 * This replaces multiple useEffect calls for better SEO performance
 * Optimized for SSG with timeout and error handling
 */
export async function getHomePageData(): Promise<HomePageData> {
    try {
        // Add timeout for build-time reliability
        const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error('Timeout fetching home page data')), 15000);
        });

        // Fetch all data in parallel for better performance with individual timeouts
        const dataPromise = Promise.all([
            withTimeout(getHeroSectionData(), 8000),
            withTimeout(getBusinessSectionData(), 8000),
            withTimeout(getWhySectionData(), 8000),
            withTimeout(getEssentialSupportData(), 8000),
            withTimeout(getSetupProcessData(), 8000),
            withTimeout(getNewCompanyData(), 8000),
            withTimeout(getDynamicCellData(), 8000)
        ]);

        const [
            heroData,
            businessData,
            whySectionData,
            essentialSupportData,
            setupProcessData,
            newCompanyData,
            dynamicCellData
        ] = await Promise.race([dataPromise, timeoutPromise]);

        return {
            hero: heroData,
            business: businessData,
            whySection: whySectionData,
            essentialSupport: essentialSupportData,
            setupProcess: setupProcessData,
            newCompany: newCompanyData,
            dynamicCell: dynamicCellData
        };
    } catch (error) {
        console.error("Error fetching home page data:", error);
        // Return null values instead of throwing to prevent page crashes
        return {
            hero: null,
            business: null,
            whySection: null,
            essentialSupport: null,
            setupProcess: null,
            newCompany: { section: null, images: null },
            dynamicCell: null
        };
    }
}

/**
 * Fetch hero section data
 */
async function getHeroSectionData(): Promise<HeroSection | null> {
    try {
        // First try RPC function with timeout
        try {
            const { data: rpcData, error: rpcError } = await withTimeout(
                supabase.rpc('get_hero_section'),
                5000
            );

            if (!rpcError && rpcData && rpcData.length > 0) {
                return rpcData[0] as HeroSection;
            }
        } catch (rpcTimeoutError) {
            console.log("RPC timed out, trying direct table query for hero section");
        }

        // Fallback to direct table query if RPC fails or times out
        console.log("Using direct table query for hero section");
        const { data: tableData, error: tableError } = await withTimeout(
            supabase
                .from("hero_sections")
                .select(`
                    *,
                    hero_typing_texts (
                        id,
                        text,
                        display_order
                    )
                `)
                .eq("is_active", true)
                .order("created_at", { ascending: false })
                .limit(1)
                .single(),
            5000
        );

        if (tableError) {
            console.error('Error fetching hero section from table:', tableError);
            return null;
        }

        return tableData as HeroSection;
    } catch (error) {
        console.error('Error in getHeroSectionData:', error);
        return null;
    }
}

/**
 * Fetch business section data
 */
async function getBusinessSectionData(): Promise<BusinessSection | null> {
    try {
        // First try RPC function with timeout
        try {
            const { data: rpcData, error: rpcError } = await withTimeout(
                supabase.rpc("get_business_section"),
                5000
            );

            if (!rpcError && rpcData && rpcData.length > 0) {
                return rpcData[0] as BusinessSection;
            }
        } catch (rpcTimeoutError) {
            console.log("RPC timed out, trying direct table query for business section");
        }

        // Fallback to direct table query if RPC fails or times out
        console.log("Using direct table query for business section");
        const { data: tableData, error: tableError } = await withTimeout(
            supabase
                .from("business_sections")
                .select("*")
                .eq("is_active", true)
                .order("updated_at", { ascending: false })
                .limit(1)
                .single(),
            5000
        );

        if (tableError) {
            console.error("Error fetching business section from table:", tableError);
            return null;
        }

        return tableData as BusinessSection;
    } catch (error) {
        console.error("Error in getBusinessSectionData:", error);
        return null;
    }
}

/**
 * Fetch why section data
 */
async function getWhySectionData(): Promise<WhySection | null> {
    try {
        // First try RPC function with timeout
        try {
            const { data: rpcData, error: rpcError } = await withTimeout(
                supabase.rpc("get_why_section"),
                5000
            );

            if (!rpcError && rpcData && rpcData.length > 0) {
                return rpcData[0] as WhySection;
            }
        } catch (rpcTimeoutError) {
            console.log("RPC timed out, trying direct table query for why section");
        }

        // Fallback to direct table query if RPC fails or times out
        console.log("Using direct table query for why section");
        const { data: tableData, error: tableError } = await withTimeout(
            supabase
                .from("why_sections")
                .select("*")
                .eq("is_active", true)
                .order("updated_at", { ascending: false })
                .limit(1)
                .single(),
            5000
        );

        if (tableError) {
            console.error("Error fetching why section from table:", tableError);
            return null;
        }

        return tableData as WhySection;
    } catch (error) {
        console.error("Error in getWhySectionData:", error);
        return null;
    }
}

/**
 * Fetch essential support data
 */
async function getEssentialSupportData(): Promise<EssentialSupportSection | null> {
    try {
        // Use direct table query with timeout
        const { data, error } = await withTimeout(
            supabase
                .from("essential_support_data")
                .select("*")
                .eq("is_active", true)
                .order("updated_at", { ascending: false })
                .limit(1)
                .single(),
            5000
        );

        if (error) {
            console.error("Error fetching essential support section:", error);
            // Return null instead of throwing to prevent page crashes
            return null;
        }

        return data as EssentialSupportSection;
    } catch (error) {
        console.error("Error in getEssentialSupportData:", error);
        // Return null instead of throwing to prevent page crashes
        return null;
    }
}



/**
 * Fetch new company data with images
 */
async function getNewCompanyData(): Promise<{ section: NewCompanySection | null; images: any | null }> {
    try {
        // First get the section data using correct table name with timeout
        const { data: sectionData, error: sectionError } = await withTimeout(
            supabase
                .from("new_company_section")
                .select("*")
                .eq("is_active", true)
                .order("created_at", { ascending: false })
                .limit(1)
                .single(),
            5000
        );

        if (sectionError || !sectionData) {
            console.error("Error fetching new company section:", sectionError);
            return { section: null, images: null };
        }

        // Then get the images for this section with timeout
        const { data: imagesData, error: imagesError } = await withTimeout(
            supabase
                .from("new_company_images")
                .select("*")
                .eq("section_id", sectionData.id)
                .eq("is_active", true)
                .order("display_order"),
            5000
        );

        if (imagesError) {
            console.error("Error fetching new company images:", imagesError);
            return { section: sectionData as NewCompanySection, images: null };
        }

        // Group images by column number (1, 2, 3)
        const imagesByColumn = {
            1: imagesData?.filter(img => img.column_number === 1) || [],
            2: imagesData?.filter(img => img.column_number === 2) || [],
            3: imagesData?.filter(img => img.column_number === 3) || []
        };

        return {
            section: sectionData as NewCompanySection,
            images: imagesByColumn
        };
    } catch (error) {
        console.error("Error in getNewCompanyData:", error);
        return { section: null, images: null };
    }
}

/**
 * Fetch dynamic cell data
 */
async function getDynamicCellData(): Promise<DynamicCellDisplayData | null> {
    try {
        // First try RPC function with timeout
        try {
            const { data: rpcData, error: rpcError } = await withTimeout(
                supabase.rpc("get_dynamic_cell_section"),
                5000
            );

            if (!rpcError && rpcData && rpcData.length > 0) {
                return rpcData[0] as DynamicCellDisplayData;
            }
        } catch (rpcTimeoutError) {
            console.log("RPC timed out, trying direct table query for dynamic cell");
        }

        // Fallback to direct table query if RPC fails or times out
        console.log("Using direct table query for dynamic cell");
        const { data: tableData, error: tableError } = await withTimeout(
            supabase
                .from("dynamic_cell_section")
                .select("*")
                .eq("is_active", true)
                .order("updated_at", { ascending: false })
                .limit(1)
                .single(),
            5000
        );

        if (tableError) {
            console.error("Error fetching dynamic cell from table:", tableError);
            return null;
        }

        return tableData as DynamicCellDisplayData;
    } catch (error) {
        console.error("Error in getDynamicCellData:", error);
        return null;
    }
}
