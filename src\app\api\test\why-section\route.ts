import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

// GET /api/test/why-section - Test why section data fetching
export async function GET(request: NextRequest) {
    try {
        const supabase = await createClient();
        
        // Test RPC function first
        console.log("Testing RPC function get_why_section...");
        const { data: rpcData, error: rpcError } = await supabase.rpc("get_why_section");
        
        let rpcResult = {
            success: !rpcError,
            data: rpcData,
            error: rpcError?.message || null
        };

        // Test direct table query
        console.log("Testing direct table query...");
        const { data: tableData, error: tableError } = await supabase
            .from("why_sections")
            .select("*")
            .eq("is_active", true)
            .order("updated_at", { ascending: false })
            .limit(1);

        let tableResult = {
            success: !tableError,
            data: tableData,
            error: tableError?.message || null
        };

        return NextResponse.json({
            status: "test_complete",
            timestamp: new Date().toISOString(),
            tests: {
                rpc_function: rpcResult,
                direct_table: tableResult
            },
            recommendation: rpcResult.success ? "Use RPC function" : "Use direct table query"
        });

    } catch (error) {
        console.error("Why section test error:", error);
        return NextResponse.json({
            status: "error",
            message: "Test failed",
            error: error instanceof Error ? error.message : "Unknown error",
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}
