import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

// GET /api/health - Health check endpoint to test database connectivity
export async function GET(request: NextRequest) {
    try {
        const supabase = await createClient();
        
        // Test basic database connectivity
        const { data, error } = await supabase
            .from("hero_sections")
            .select("id")
            .limit(1);

        if (error) {
            console.error("Database health check failed:", error);
            return NextResponse.json({
                status: "error",
                message: "Database connection failed",
                error: error.message,
                timestamp: new Date().toISOString()
            }, { status: 500 });
        }

        return NextResponse.json({
            status: "healthy",
            message: "Database connection successful",
            timestamp: new Date().toISOString(),
            database: {
                connected: true,
                recordsFound: data?.length || 0
            }
        });

    } catch (error) {
        console.error("Health check error:", error);
        return NextResponse.json({
            status: "error",
            message: "Health check failed",
            error: error instanceof Error ? error.message : "Unknown error",
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}
